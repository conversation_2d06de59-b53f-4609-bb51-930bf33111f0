<script setup lang="ts">
import { ref, reactive } from 'vue'
import FileUpload from '@/components/FileUpload.vue'
import ConfigPanel from '@/components/ConfigPanel.vue'
import EditorPreview from '@/components/EditorPreview.vue'
import type { LayoutConfig } from '@/types/layout'

// 响应式数据
const markdownContent = ref('')
const layoutConfig = reactive<LayoutConfig>({
  page_format: 'A4',
  margin_top: 2.0,
  margin_bottom: 2.0,
  margin_left: 2.0,
  margin_right: 2.0,
  font_family: 'Noto Sans CJK SC',
  font_size: 12,
  line_height: 1.5,
  paragraph_spacing: 6,
  indent_first_line: true,
  dpi: 300,
  color_mode: 'CMYK',
  bleed: 3,
  enable_hyphenation: true,
  widow_orphan_control: true
})

// 处理文件上传
const handleFileUpload = (content: string) => {
  markdownContent.value = content
}

// 处理配置更新
const handleConfigUpdate = (newConfig: Partial<LayoutConfig>) => {
  Object.assign(layoutConfig, newConfig)
}
</script>

<template>
  <div class="h-screen bg-gray-50 overflow-hidden">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b">
      <div class="w-full px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold text-gray-900">PrintMind</h1>
            <span class="ml-2 text-sm text-gray-500">智能排版工具</span>
          </div>
          <div class="flex items-center space-x-4">
            <button class="btn-secondary">帮助</button>
            <button class="btn-primary">设置</button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="w-full px-4 sm:px-6 lg:px-8 py-4">
      <div class="grid grid-cols-1 gap-6 h-full">

        <!-- 左侧：文件上传和配置 -->
        <div class="space-y-4 overflow-y-auto h-full">
          <!-- 文件上传区域 -->
          <div class="card p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">文档上传</h2>
            <FileUpload @file-uploaded="handleFileUpload" />
          </div>

          <!-- 排版配置面板 -->
          <div class="card p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">排版配置</h2>
            <ConfigPanel
              :config="layoutConfig"
              @config-updated="handleConfigUpdate"
            />
          </div>


        </div>

        <!-- 右侧：编辑器和预览 -->
        <div>
          <div class="card editor-card">
            <EditorPreview
              v-model="markdownContent"
              :config="layoutConfig"
              class="h-full"
            />
          </div>
        </div>

      </div>
    </main>
  </div>
</template>

<style scoped>
/* 组件特定样式 */
.grid {
  height: calc(100vh - 6rem); /* 顶部导航4rem + padding 2rem */
}

main {
  height: calc(100vh - 4rem); /* 顶部导航4rem */
  overflow: hidden; /* 防止内容溢出 */
  box-sizing: border-box; /* 包含padding在高度计算中 */
}

.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
  overflow: hidden; /* 防止内容溢出 */
}

/* 只有右侧的编辑器卡片需要填满高度 */
.editor-card {
  height: 100%;
}

/* 响应式布局优化 */
@media (min-width: 1024px) {
  .grid {
    grid-template-columns: minmax(320px, 1fr) 4fr;
  }
}

@media (min-width: 1536px) {
  .grid {
    grid-template-columns: minmax(360px, 1fr) 5fr;
  }
}

/* 确保在超宽屏幕上有合理的最大宽度 */
@media (min-width: 2560px) {
  main {
    max-width: 2400px;
    margin: 0 auto;
  }
}
</style>
