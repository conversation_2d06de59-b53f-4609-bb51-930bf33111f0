# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# PDF 生成和处理 (暂时禁用 WeasyPrint)
# weasyprint==60.2
reportlab==4.0.7
# pypdf==3.17.1

# 文档处理
python-docx==1.1.0
markdown==3.5.1
python-markdown-math==0.8

# AI 集成
openai==1.3.7
httpx==0.25.2

# 数据处理
pydantic==2.5.0
pydantic-settings==2.1.0

# 文件处理
aiofiles==23.2.1
pillow==10.1.0
aiohttp==3.9.1

# 字体处理已简化，不再需要 fonttools

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
