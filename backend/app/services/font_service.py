"""
字体管理服务
"""

import os
import platform
from typing import List, Dict, Any, Optional
from fontTools.ttLib import TTFont
import glob

from app.models.schemas import FontInfo
from app.core.config import settings

class FontService:
    """字体管理服务类"""
    
    def __init__(self):
        self.system_font_paths = self._get_system_font_paths()
        self.custom_font_path = settings.FONT_DIR
        
        # 创建自定义字体目录
        os.makedirs(self.custom_font_path, exist_ok=True)
    
    def _get_system_font_paths(self) -> List[str]:
        """获取系统字体路径"""
        system = platform.system()
        
        if system == "Windows":
            return [
                "C:/Windows/Fonts/",
                os.path.expanduser("~/AppData/Local/Microsoft/Windows/Fonts/")
            ]
        elif system == "Darwin":  # macOS
            return [
                "/System/Library/Fonts/",
                "/Library/Fonts/",
                os.path.expanduser("~/Library/Fonts/")
            ]
        else:  # Linux
            return [
                "/usr/share/fonts/",
                "/usr/local/share/fonts/",
                os.path.expanduser("~/.fonts/"),
                os.path.expanduser("~/.local/share/fonts/")
            ]
    
    def get_available_fonts(self) -> List[FontInfo]:
        """获取所有可用字体"""
        fonts = []
        
        # 扫描系统字体
        for font_path in self.system_font_paths:
            if os.path.exists(font_path):
                fonts.extend(self._scan_font_directory(font_path))
        
        # 扫描自定义字体
        if os.path.exists(self.custom_font_path):
            fonts.extend(self._scan_font_directory(self.custom_font_path))
        
        # 去重并排序
        unique_fonts = {}
        for font in fonts:
            key = f"{font.family}_{font.style}"
            if key not in unique_fonts:
                unique_fonts[key] = font
        
        return sorted(unique_fonts.values(), key=lambda x: x.family)
    
    def _scan_font_directory(self, directory: str) -> List[FontInfo]:
        """扫描字体目录"""
        fonts = []
        
        # 支持的字体格式
        font_extensions = ["*.ttf", "*.otf", "*.ttc", "*.woff", "*.woff2"]
        
        for ext in font_extensions:
            pattern = os.path.join(directory, "**", ext)
            for font_file in glob.glob(pattern, recursive=True):
                try:
                    font_info = self._extract_font_info(font_file)
                    if font_info:
                        fonts.append(font_info)
                except Exception:
                    # 忽略无法解析的字体文件
                    continue
        
        return fonts
    
    def _extract_font_info(self, font_path: str) -> Optional[FontInfo]:
        """提取字体信息"""
        try:
            font = TTFont(font_path)
            
            # 获取字体名称表
            name_table = font['name']
            
            # 提取字体族名称
            family_name = self._get_font_name(name_table, 1) or "Unknown"
            
            # 提取字体样式
            style_name = self._get_font_name(name_table, 2) or "Regular"
            
            # 提取完整字体名称
            full_name = self._get_font_name(name_table, 4) or family_name
            
            # 检查是否支持中文
            supports_chinese = self._check_chinese_support(font)
            
            return FontInfo(
                name=full_name,
                family=family_name,
                style=style_name,
                file_path=font_path,
                supports_chinese=supports_chinese
            )
            
        except Exception:
            return None
    
    def _get_font_name(self, name_table, name_id: int) -> Optional[str]:
        """从字体名称表中获取指定ID的名称"""
        for record in name_table.names:
            if record.nameID == name_id:
                if record.platformID == 3:  # Microsoft platform
                    try:
                        return record.string.decode('utf-16-be')
                    except:
                        pass
                elif record.platformID == 1:  # Apple platform
                    try:
                        return record.string.decode('mac-roman')
                    except:
                        pass
        return None
    
    def _check_chinese_support(self, font: TTFont) -> bool:
        """检查字体是否支持中文"""
        try:
            cmap = font.getBestCmap()
            if not cmap:
                return False
            
            # 检查常用中文字符
            chinese_test_chars = [
                0x4E00,  # 一
                0x4E2D,  # 中
                0x6587,  # 文
                0x5B57,  # 字
                0x4F53   # 体
            ]
            
            supported_count = sum(1 for char in chinese_test_chars if char in cmap)
            return supported_count >= 3  # 至少支持3个测试字符
            
        except Exception:
            return False
    
    def get_system_fonts(self) -> List[Dict[str, Any]]:
        """获取系统字体列表（简化版）"""
        system_fonts = []
        
        # 常用系统字体 - 只保留最常用的字体
        common_fonts = [
            "Arial", "Times New Roman", "Helvetica",
            "Courier New", "SimSun", "SimHei",
            "Microsoft YaHei", "KaiTi", "STSong", "STHeiti"
        ]
        
        for font_name in common_fonts:
            if self.validate_font(font_name):
                system_fonts.append({
                    "name": font_name,
                    "family": font_name,
                    "available": True
                })
        
        return system_fonts
    
    def get_chinese_fonts(self) -> List[FontInfo]:
        """获取支持中文的字体"""
        all_fonts = self.get_available_fonts()
        return [font for font in all_fonts if font.supports_chinese]
    
    def validate_font(self, font_name: str) -> bool:
        """验证字体是否可用"""
        try:
            # 简单的字体验证：检查是否在可用字体列表中
            available_fonts = self.get_available_fonts()
            
            for font in available_fonts:
                if (font.name.lower() == font_name.lower() or 
                    font.family.lower() == font_name.lower()):
                    return True
            
            return False
            
        except Exception:
            return False
    
    def get_font_info(self, font_name: str) -> Optional[Dict[str, Any]]:
        """获取字体详细信息"""
        available_fonts = self.get_available_fonts()
        
        for font in available_fonts:
            if (font.name.lower() == font_name.lower() or 
                font.family.lower() == font_name.lower()):
                
                return {
                    "name": font.name,
                    "family": font.family,
                    "style": font.style,
                    "file_path": font.file_path,
                    "supports_chinese": font.supports_chinese,
                    "file_size": os.path.getsize(font.file_path) if os.path.exists(font.file_path) else 0
                }
        
        return None
    
    def get_recommended_fonts(self) -> Dict[str, List[str]]:
        """获取推荐字体配置 - 只保留最常用的字体"""
        return {
            "chinese_serif": [
                "SimSun", "STSong"
            ],
            "chinese_sans": [
                "Microsoft YaHei", "SimHei", "STHeiti"
            ],
            "english_serif": [
                "Times New Roman", "Georgia"
            ],
            "english_sans": [
                "Arial", "Helvetica"
            ],
            "monospace": [
                "Courier New"
            ]
        }
